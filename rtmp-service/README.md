# WebRTC to RTMP Streaming Service

A Node.js service that bridges WebRTC video streams to RTMP endpoints for live streaming platforms like Twitch, YouTube Live, and other RTMP-compatible services.

## Overview

This service creates a WebRTC-to-RTMP bridge that:
1. Accepts WebRTC video streams from web clients
2. Processes video frames in real-time
3. Encodes and streams to RTMP endpoints using FFmpeg
4. Provides session management and monitoring

## Architecture

```
Web Client (WebRTC) → Node.js Service → FFmpeg → RTMP Server (Twitch/YouTube)
```

### Key Components

- **WebRTC Handler**: Manages peer connections and video track reception
- **Video Processor**: Handles frame capture and processing (currently enhanced test pattern)
- **FFmpeg Bridge**: Encodes and streams video to RTMP endpoints
- **Session Manager**: Tracks active streaming sessions
- **Socket.IO Server**: Handles real-time communication with clients

## Features

- ✅ WebRTC peer connection establishment
- ✅ Video track reception and processing
- ✅ Real-time FFmpeg encoding (H.264/AAC)
- ✅ RTMP streaming to multiple platforms
- ✅ Session management and monitoring
- ✅ Detailed logging and error handling
- ✅ Enhanced test pattern generation
- ⚠️ Real video frame capture (see [VIDEO_CAPTURE_APPROACHES.md](./VIDEO_CAPTURE_APPROACHES.md))

## Installation

1. **Install dependencies:**
```bash
npm install
```

2. **Ensure FFmpeg is installed:**
```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# Verify installation
ffmpeg -version
```

3. **Start the service:**
```bash
npm start
# or for development
npm run dev
```

The service runs on port 3002 by default.

### API Endpoints

- `GET /` - Service status and information
- `GET /health` - Health check endpoint
- `GET /sessions` - List all active streaming sessions
- `GET /sessions/:sessionId` - Get specific session status
- `DELETE /sessions/:sessionId` - Stop a specific session

### WebSocket Events

The service communicates via Socket.io with the following events:

**Client → Service:**
- `start-stream` - Start a new streaming session
- `webrtc-offer` - WebRTC offer for peer connection
- `webrtc-ice-candidate` - ICE candidate for connection establishment
- `stop-stream` - Stop an active streaming session

**Service → Client:**
- `stream-session-created` - Confirmation of session creation
- `webrtc-answer` - WebRTC answer for peer connection
- `webrtc-error` - Error in WebRTC connection
- `stream-stopped` - Confirmation of stream stop

## Configuration

### Environment Variables

- `PORT` - Service port (default: 3002)
- `NODE_ENV` - Environment (development/production)
- `TURN_SERVER` - TURN server IP/domain for WebRTC
- `TURN_USERNAME` - TURN server username
- `TURN_PASSWORD` - TURN server password

### FFmpeg Settings

The service uses optimized FFmpeg settings for RTMP streaming:

- **Video Codec**: H.264 with baseline profile
- **Audio Codec**: AAC
- **Preset**: veryfast (for low latency)
- **Bitrate**: 2.5 Mbps video, 128 kbps audio
- **Resolution**: 1920x1080 @ 30fps
- **GOP Size**: 60 frames (2 seconds at 30fps)

## Production Deployment

### Google Compute Engine

Deploy to GCE using the provided deployment script:

```bash
./deploy-gce.sh
```

This will:
1. Build and push the Docker image to Google Artifact Registry
2. Create a GCE instance with optimized settings
3. Configure firewall rules for the service
4. Set up automatic container restart and log rotation

### Manual Deployment

1. **Build the Docker image:**
   ```bash
   docker build -t rtmp-service .
   ```

2. **Run the container:**
   ```bash
   docker run -d \
     --name rtmp-service \
     -p 3002:3002 \
     -e NODE_ENV=production \
     -e TURN_SERVER=your-turn-server.com \
     rtmp-service
   ```

## Integration

### With Main Application

The main WebRTC application should connect to this service when starting RTMP streaming:

1. **Connect to the service** via Socket.io
2. **Start a stream session** with RTMP URL and stream key
3. **Establish WebRTC connection** using the provided session ID
4. **Send composite video stream** to the service
5. **Monitor session status** via API endpoints

### Example Integration Code

```javascript
// Connect to RTMP service
const rtmpSocket = io('http://rtmp-service:3002');

// Start streaming session
rtmpSocket.emit('start-stream', {
  rtmpUrl: 'rtmp://live.twitch.tv/live',
  streamKey: 'your-stream-key',
  roomId: 'room-123'
});

// Handle session creation
rtmpSocket.on('stream-session-created', ({ sessionId, success }) => {
  if (success) {
    // Create WebRTC peer connection to service
    setupWebRTCConnection(sessionId);
  }
});
```

## Monitoring

### Health Checks

The service provides comprehensive health monitoring:

```bash
curl http://localhost:3002/health
```

Response includes:
- Service status
- Uptime
- Active sessions count
- Memory usage
- FFmpeg availability

### Session Monitoring

Monitor active streaming sessions:

```bash
curl http://localhost:3002/sessions
```

### Logs

Container logs include detailed information about:
- WebRTC connection establishment
- FFmpeg process status
- Streaming performance metrics
- Error conditions

## Troubleshooting

### Common Issues

1. **FFmpeg not found**: Ensure FFmpeg is installed in the container
2. **WebRTC connection fails**: Check TURN server configuration
3. **RTMP connection fails**: Verify RTMP URL and stream key
4. **High CPU usage**: Consider adjusting FFmpeg encoding settings

### Debug Mode

Enable verbose logging:

```bash
docker run -e NODE_ENV=development rtmp-service
```

## Performance Optimization

### Scaling

- **Horizontal Scaling**: Deploy multiple instances behind a load balancer
- **Resource Allocation**: Allocate sufficient CPU for FFmpeg encoding
- **Network**: Ensure adequate bandwidth for RTMP output

### FFmpeg Tuning

Adjust encoding settings in `webrtc-bridge.js`:
- Lower bitrate for bandwidth-constrained environments
- Change preset to 'ultrafast' for maximum performance
- Adjust GOP size based on streaming platform requirements

## Security

- Service runs as non-root user in container
- No persistent data storage (stateless design)
- Input validation on all API endpoints
- Automatic cleanup of temporary files

---

## 🔧 Technical Implementation Status

### Current Video Capture Implementation

**⚠️ Important**: This service currently uses **enhanced test patterns** instead of real video frame capture from WebRTC streams due to technical limitations in the Node.js environment.

### What's Working ✅

1. **Complete WebRTC Pipeline**: Full connection establishment, ICE exchange, peer connection management
2. **FFmpeg Integration**: Real-time video encoding and RTMP streaming with optimized settings
3. **Enhanced Test Patterns**: Sophisticated animated patterns demonstrating the complete pipeline
4. **Session Management**: Full lifecycle management of streaming sessions
5. **Platform Compatibility**: Tested with Twitch, supports YouTube Live and other RTMP platforms

### Current Video Pipeline

```
WebRTC Video Track → Enhanced Test Pattern Generator → YUV420p Frames → FFmpeg → H.264/AAC → RTMP
```

**Enhanced Test Pattern Features:**
- **Format**: YUV420p (1.5 bytes per pixel)
- **Resolution**: 1920x1080 (configurable)
- **Frame Rate**: 30 FPS (33ms intervals)
- **Animation**: Multiple moving elements (waves, gradients, stripes, noise)
- **Real-time**: Time-based animations that change continuously

### Video Capture Approaches Attempted

For detailed technical information about the various approaches we tried to capture real video frames, see **[VIDEO_CAPTURE_APPROACHES.md](./VIDEO_CAPTURE_APPROACHES.md)**.

**Summary of attempts:**
1. ❌ **MediaStreamTrackProcessor** - Not available in Node.js
2. ❌ **WRTC Internal Callbacks** - Not exposed in public API
3. ❌ **Canvas Frame Extraction** - OffscreenCanvas not available in Node.js
4. ❌ **Direct MediaStream Processing** - No frame-level access in Node.js wrtc

### Why Enhanced Test Patterns?

The Node.js `wrtc` library doesn't provide the same frame access APIs available in browsers. While WebRTC connections work perfectly and video tracks are received, extracting individual frames requires browser-specific APIs not available in Node.js.

**The enhanced test pattern approach demonstrates:**
- ✅ Complete WebRTC-to-RTMP pipeline functionality
- ✅ Real-time video processing and encoding capabilities
- ✅ RTMP streaming to live platforms
- ✅ Session management and monitoring
- ✅ All infrastructure needed for real video capture

### Troubleshooting RTMP Connection Issues

**Common Problems:**

1. **"Input/output error" when connecting to RTMP:**
   ```bash
   # Test basic connectivity
   nc -v live.twitch.tv 1935

   # Test with FFmpeg directly
   ffmpeg -f lavfi -i testsrc=duration=10:size=1920x1080:rate=30 \
          -c:v libx264 -preset ultrafast -f flv \
          rtmp://live.twitch.tv/app/YOUR_STREAM_KEY
   ```

2. **Stream not appearing on platform:**
   - Ensure Twitch account is set to "Go Live"
   - Verify stream key is current (check Creator Dashboard)
   - Confirm stream key hasn't expired

3. **WebRTC connection established but no RTMP output:**
   - Check FFmpeg stderr logs for detailed errors
   - Verify RTMP URL format and accessibility
   - Monitor network connectivity to streaming platform

### Performance Monitoring

**Real-time Statistics Example:**
```
📊 Sent 270 enhanced frames (1920x1080) - 51.4s
🔄 WebRTC connection state: connected for session abc123
📺 FFmpeg: frame= 150 fps= 30 q=14.0 size= 1024KiB time=00:00:05.00 bitrate=1677.7kbits/s
```

### Future Development Path

**For Real Video Capture:**
1. **Native Addon**: Create Node.js addon to access wrtc library internals
2. **Browser Bridge**: Use headless browser (Puppeteer) for frame extraction
3. **Media Server**: Implement dedicated WebRTC media server (Kurento, Janus)
4. **Library Updates**: Wait for wrtc library improvements with frame access APIs

**Current Status**: The enhanced test pattern provides a fully functional streaming pipeline that can be easily extended once real frame capture becomes available.

### Testing Commands

```bash
# Test the enhanced pattern streaming
node test-streaming.js

# Monitor service with detailed logging
npm start

# Test RTMP connectivity
nc -v live.twitch.tv 1935

# Verify FFmpeg installation
ffmpeg -version
```
