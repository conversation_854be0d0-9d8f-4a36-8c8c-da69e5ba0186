# WebRTC to RTMP Streaming Service

A scalable, dockerized service that receives WebRTC video streams and converts them to RTMP for streaming to external platforms like Twitch, YouTube, and custom RTMP servers.

## Features

- **WebRTC Stream Reception**: Accepts WebRTC peer connections from the main application
- **FFmpeg Integration**: High-performance video/audio processing and encoding
- **RTMP Output**: Streams to any RTMP endpoint with optimized settings
- **Scalable Architecture**: Designed for horizontal scaling and cloud deployment
- **Health Monitoring**: Built-in health checks and session monitoring
- **Production Ready**: Optimized for low latency and high reliability

## Architecture

```
WebRTC Client (Host) → Signaling Server → RTMP Service → FFmpeg → RTMP Endpoint
```

The service acts as a WebRTC peer that receives the composite video stream from the host application and converts it to RTMP using FFmpeg with optimized encoding settings.

## Quick Start

### Local Development

1. **Start the service with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

2. **Or run directly with Node.js:**
   ```bash
   npm install
   npm start
   ```

The service will be available at `http://localhost:3002`

### API Endpoints

- `GET /` - Service status and information
- `GET /health` - Health check endpoint
- `GET /sessions` - List all active streaming sessions
- `GET /sessions/:sessionId` - Get specific session status
- `DELETE /sessions/:sessionId` - Stop a specific session

### WebSocket Events

The service communicates via Socket.io with the following events:

**Client → Service:**
- `start-stream` - Start a new streaming session
- `webrtc-offer` - WebRTC offer for peer connection
- `webrtc-ice-candidate` - ICE candidate for connection establishment
- `stop-stream` - Stop an active streaming session

**Service → Client:**
- `stream-session-created` - Confirmation of session creation
- `webrtc-answer` - WebRTC answer for peer connection
- `webrtc-error` - Error in WebRTC connection
- `stream-stopped` - Confirmation of stream stop

## Configuration

### Environment Variables

- `PORT` - Service port (default: 3002)
- `NODE_ENV` - Environment (development/production)
- `TURN_SERVER` - TURN server IP/domain for WebRTC
- `TURN_USERNAME` - TURN server username
- `TURN_PASSWORD` - TURN server password

### FFmpeg Settings

The service uses optimized FFmpeg settings for RTMP streaming:

- **Video Codec**: H.264 with baseline profile
- **Audio Codec**: AAC
- **Preset**: veryfast (for low latency)
- **Bitrate**: 2.5 Mbps video, 128 kbps audio
- **Resolution**: 1920x1080 @ 30fps
- **GOP Size**: 60 frames (2 seconds at 30fps)

## Production Deployment

### Google Compute Engine

Deploy to GCE using the provided deployment script:

```bash
./deploy-gce.sh
```

This will:
1. Build and push the Docker image to Google Artifact Registry
2. Create a GCE instance with optimized settings
3. Configure firewall rules for the service
4. Set up automatic container restart and log rotation

### Manual Deployment

1. **Build the Docker image:**
   ```bash
   docker build -t rtmp-service .
   ```

2. **Run the container:**
   ```bash
   docker run -d \
     --name rtmp-service \
     -p 3002:3002 \
     -e NODE_ENV=production \
     -e TURN_SERVER=your-turn-server.com \
     rtmp-service
   ```

## Integration

### With Main Application

The main WebRTC application should connect to this service when starting RTMP streaming:

1. **Connect to the service** via Socket.io
2. **Start a stream session** with RTMP URL and stream key
3. **Establish WebRTC connection** using the provided session ID
4. **Send composite video stream** to the service
5. **Monitor session status** via API endpoints

### Example Integration Code

```javascript
// Connect to RTMP service
const rtmpSocket = io('http://rtmp-service:3002');

// Start streaming session
rtmpSocket.emit('start-stream', {
  rtmpUrl: 'rtmp://live.twitch.tv/live',
  streamKey: 'your-stream-key',
  roomId: 'room-123'
});

// Handle session creation
rtmpSocket.on('stream-session-created', ({ sessionId, success }) => {
  if (success) {
    // Create WebRTC peer connection to service
    setupWebRTCConnection(sessionId);
  }
});
```

## Monitoring

### Health Checks

The service provides comprehensive health monitoring:

```bash
curl http://localhost:3002/health
```

Response includes:
- Service status
- Uptime
- Active sessions count
- Memory usage
- FFmpeg availability

### Session Monitoring

Monitor active streaming sessions:

```bash
curl http://localhost:3002/sessions
```

### Logs

Container logs include detailed information about:
- WebRTC connection establishment
- FFmpeg process status
- Streaming performance metrics
- Error conditions

## Troubleshooting

### Common Issues

1. **FFmpeg not found**: Ensure FFmpeg is installed in the container
2. **WebRTC connection fails**: Check TURN server configuration
3. **RTMP connection fails**: Verify RTMP URL and stream key
4. **High CPU usage**: Consider adjusting FFmpeg encoding settings

### Debug Mode

Enable verbose logging:

```bash
docker run -e NODE_ENV=development rtmp-service
```

## Performance Optimization

### Scaling

- **Horizontal Scaling**: Deploy multiple instances behind a load balancer
- **Resource Allocation**: Allocate sufficient CPU for FFmpeg encoding
- **Network**: Ensure adequate bandwidth for RTMP output

### FFmpeg Tuning

Adjust encoding settings in `webrtc-bridge.js`:
- Lower bitrate for bandwidth-constrained environments
- Change preset to 'ultrafast' for maximum performance
- Adjust GOP size based on streaming platform requirements

## Security

- Service runs as non-root user in container
- No persistent data storage (stateless design)
- Input validation on all API endpoints
- Automatic cleanup of temporary files
