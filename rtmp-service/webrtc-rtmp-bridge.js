const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Import WebRTC
const { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate } = require('wrtc');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: true,
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Store active streaming sessions
const streamingSessions = new Map();

class WebRTCToRTMPBridge {
  constructor(sessionId, rtmpUrl, streamKey) {
    this.sessionId = sessionId;
    this.rtmpUrl = rtmpUrl;
    this.streamKey = streamKey;
    this.fullRtmpUrl = `${rtmpUrl}/${streamKey}`;
    this.peerConnection = null;
    this.ffmpegProcess = null;
    this.isStreaming = false;
    this.startedAt = null;
    this.videoTrack = null;
    this.audioTrack = null;
    
    console.log(`🌉 Created WebRTC-to-RTMP bridge ${sessionId} for ${this.fullRtmpUrl}`);
  }

  async createPeerConnection() {
    console.log(`🔗 Creating WebRTC peer connection for session ${this.sessionId}`);
    
    // Configure ICE servers
    const iceServers = [
      { urls: 'stun:stun.l.google.com:19302' }
    ];
    
    // Add TURN server if configured
    const turnServer = process.env.TURN_SERVER;
    if (turnServer) {
      iceServers.push({
        urls: [`turn:${turnServer}:3478`],
        username: process.env.TURN_USERNAME || 'webrtc',
        credential: process.env.TURN_PASSWORD || 'webrtc123'
      });
    }

    this.peerConnection = new RTCPeerConnection({ iceServers });

    // Handle incoming tracks
    this.peerConnection.ontrack = (event) => {
      console.log(`📹 Received ${event.track.kind} track for session ${this.sessionId}`);
      
      if (event.track.kind === 'video') {
        this.videoTrack = event.track;
      } else if (event.track.kind === 'audio') {
        this.audioTrack = event.track;
      }

      // Start FFmpeg when we have video track
      if (this.videoTrack && !this.isStreaming) {
        this.startDirectFFmpegStream();
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      if (!this.peerConnection) {
        return; // Connection was already cleaned up
      }

      console.log(`🔄 WebRTC connection state: ${this.peerConnection.connectionState} for session ${this.sessionId}`);

      if (this.peerConnection.connectionState === 'failed' ||
          this.peerConnection.connectionState === 'disconnected') {
        this.cleanup();
      }
    };

    return this.peerConnection;
  }

  async handleOffer(offer) {
    console.log(`📨 Handling WebRTC offer for session ${this.sessionId}`);
    
    if (!this.peerConnection) {
      await this.createPeerConnection();
    }

    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);

    console.log(`📤 Created WebRTC answer for session ${this.sessionId}`);
    return answer;
  }

  async handleIceCandidate(candidate) {
    if (this.peerConnection && candidate) {
      try {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        console.log(`🧊 Added ICE candidate for session ${this.sessionId}`);
      } catch (error) {
        console.error(`❌ Error adding ICE candidate:`, error);
      }
    }
  }

  startDirectFFmpegStream() {
    if (this.isStreaming || !this.videoTrack) {
      return;
    }

    console.log(`🚀 Starting direct WebRTC-to-RTMP stream for session ${this.sessionId}`);

    try {
      // Create FFmpeg process with WebRTC input simulation
      // We'll use a named pipe approach but with proper WebRTC handling
      const tempDir = path.join(__dirname, 'temp', this.sessionId);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Start FFmpeg with high-quality settings optimized for live streaming
      const ffmpegArgs = [
        // Input: We'll feed raw video frames via stdin
        '-f', 'rawvideo',
        '-pixel_format', 'yuv420p',
        '-video_size', '1920x1080',
        '-framerate', '30',
        '-i', 'pipe:0',
        
        // Audio input (we'll add this when we have audio track)
        ...(this.audioTrack ? [
          '-f', 's16le',
          '-ar', '48000',
          '-ac', '2',
          '-i', 'pipe:1'
        ] : [
          // Generate silent audio if no audio track
          '-f', 'lavfi',
          '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000'
        ]),

        // Video encoding - optimized for live streaming
        '-c:v', 'libx264',
        '-preset', 'ultrafast', // Fastest encoding for low latency
        '-tune', 'zerolatency',
        '-profile:v', 'baseline',
        '-level', '3.1',
        '-pix_fmt', 'yuv420p',
        
        // Keyframe settings for live streaming
        '-g', '60', // Keyframe every 2 seconds at 30fps
        '-keyint_min', '60',
        '-sc_threshold', '0',
        
        // Bitrate settings
        '-b:v', '2500k',
        '-maxrate', '3000k',
        '-bufsize', '6000k',
        
        // Audio encoding
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        
        // Output format
        '-f', 'flv',
        '-rtmp_live', 'live',
        
        // Output URL
        this.fullRtmpUrl
      ];

      console.log(`FFmpeg command: ffmpeg ${ffmpegArgs.join(' ')}`);

      this.ffmpegProcess = spawn('ffmpeg', ffmpegArgs, {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.isStreaming = true;
      this.startedAt = new Date();

      // Handle FFmpeg events
      this.ffmpegProcess.on('spawn', () => {
        console.log(`✅ FFmpeg spawned for session ${this.sessionId}`);
        this.startVideoCapture();
      });

      this.ffmpegProcess.on('error', (error) => {
        console.error(`❌ FFmpeg error:`, error);
        this.isStreaming = false;
        this.cleanup();
      });

      this.ffmpegProcess.on('exit', (code, signal) => {
        console.log(`🏁 FFmpeg exited with code ${code}, signal ${signal}`);
        this.isStreaming = false;
      });

      // Handle stderr for logging
      this.ffmpegProcess.stderr.on('data', (data) => {
        const message = data.toString();
        if (message.includes('fps=') || message.includes('bitrate=')) {
          console.log(`FFmpeg [${this.sessionId}]: ${message.trim()}`);
        }
      });

    } catch (error) {
      console.error(`❌ Error starting FFmpeg:`, error);
      this.isStreaming = false;
    }
  }

  startVideoCapture() {
    console.log(`🎥 Starting real video capture from WebRTC track for session ${this.sessionId}`);

    if (!this.ffmpegProcess || !this.ffmpegProcess.stdin || !this.videoTrack) {
      console.error(`❌ Cannot start video capture: missing FFmpeg process or video track`);
      return;
    }

    // Create a video element to receive the WebRTC stream
    const { createCanvas, createImageData } = require('canvas');

    // We need to use a different approach since we're in Node.js
    // Let's use the MediaStreamTrackProcessor API if available, or fall back to a simpler method

    console.log(`📹 Setting up video frame processing...`);

    // For now, let's create a more realistic test pattern that changes over time
    // This will help us verify the pipeline is working before implementing full WebRTC frame capture
    const width = 1920;
    const height = 1080;
    const frameSize = width * height * 1.5; // YUV420p is 1.5 bytes per pixel

    let frameCounter = 0;

    // Send frames at 30 FPS with a changing pattern
    this.frameInterval = setInterval(() => {
      if (this.ffmpegProcess && this.ffmpegProcess.stdin && !this.ffmpegProcess.stdin.destroyed) {
        try {
          // Create a frame that changes over time to verify it's working
          const testFrame = Buffer.alloc(frameSize);

          // Create a moving pattern
          const offset = (frameCounter * 5) % 255;

          // Fill Y component with a moving gradient
          for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
              const index = y * width + x;
              testFrame[index] = ((x + y + offset) % 255);
            }
          }

          // Fill U and V components (chroma)
          for (let i = width * height; i < frameSize; i++) {
            testFrame[i] = 128 + Math.sin(frameCounter * 0.1) * 50; // Animated color
          }

          this.ffmpegProcess.stdin.write(testFrame);
          frameCounter++;

          // Log progress every 30 frames
          if (frameCounter % 30 === 0) {
            console.log(`📊 Sent ${frameCounter} animated frames to FFmpeg`);
          }

        } catch (error) {
          console.error(`❌ Error writing frame:`, error);
          clearInterval(this.frameInterval);
        }
      } else {
        clearInterval(this.frameInterval);
      }
    }, 33); // ~30 FPS

    console.log(`🎬 Started animated test pattern - you should see a moving gradient on Twitch`);
    console.log(`💡 Next step: Implement actual WebRTC frame capture from the video track`);
  }

  stop() {
    console.log(`🛑 Stopping bridge for session ${this.sessionId}`);
    this.cleanup();
  }

  cleanup() {
    console.log(`🧹 Cleaning up session ${this.sessionId}`);
    
    if (this.frameInterval) {
      clearInterval(this.frameInterval);
      this.frameInterval = null;
    }
    
    if (this.ffmpegProcess) {
      try {
        if (this.ffmpegProcess.stdin) {
          this.ffmpegProcess.stdin.end();
        }
        this.ffmpegProcess.kill('SIGTERM');
      } catch (error) {
        console.error(`Error killing FFmpeg:`, error);
      }
      this.ffmpegProcess = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.isStreaming = false;
    this.videoTrack = null;
    this.audioTrack = null;

    // Clean up temp directory
    const tempDir = path.join(__dirname, 'temp', this.sessionId);
    if (fs.existsSync(tempDir)) {
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
      } catch (error) {
        console.error(`Error cleaning up temp directory:`, error);
      }
    }
  }

  getStatus() {
    return {
      sessionId: this.sessionId,
      rtmpUrl: this.rtmpUrl,
      isStreaming: this.isStreaming,
      startedAt: this.startedAt,
      connectionState: this.peerConnection?.connectionState || 'disconnected',
      hasVideo: !!this.videoTrack,
      hasAudio: !!this.audioTrack
    };
  }
}

module.exports = { WebRTCToRTMPBridge };
