const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const ffmpeg = require('fluent-ffmpeg');
const { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate } = require('wrtc');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: true,
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Store active streaming sessions
const streamingSessions = new Map();

class RTMPStreamingSession {
  constructor(sessionId, rtmpUrl, streamKey) {
    this.sessionId = sessionId;
    this.rtmpUrl = rtmpUrl;
    this.streamKey = streamKey;
    this.fullRtmpUrl = `${rtmpUrl}/${streamKey}`;
    this.ffmpegProcess = null;
    this.isStreaming = false;
    this.startedAt = null;
    this.inputFormat = 'webm'; // Default to WebM, will be updated when first chunk arrives
    this.hasReceivedFirstChunk = false;

    // Validate RTMP URL format
    if (!rtmpUrl.startsWith('rtmp://')) {
      throw new Error(`Invalid RTMP URL format: ${rtmpUrl}. Must start with rtmp://`);
    }

    if (!streamKey || streamKey.trim().length === 0) {
      throw new Error('Stream key cannot be empty');
    }

    console.log(`📺 Created RTMP session ${sessionId} for ${this.fullRtmpUrl}`);
    console.log(`🔑 Stream key: ${streamKey.substring(0, 8)}...${streamKey.substring(streamKey.length - 4)}`);
  }

  // WebSocket-based streaming - no WebRTC peer connection needed
  initializeStreaming() {
    console.log(`🎥 Initializing MediaRecorder-based streaming for session ${this.sessionId}`);

    // Don't start FFmpeg immediately - wait for first video chunk
    // This allows us to determine the correct input format and ensure we have valid data
    console.log(`⏳ Waiting for first video chunk to determine format and start FFmpeg...`);

    return true;
  }

  // WebSocket-based streaming - no WebRTC offer/answer needed

  startFFmpegStream() {
    if (this.isStreaming) {
      return;
    }

    console.log(`🎬 Starting FFmpeg stream for session ${this.sessionId} to ${this.fullRtmpUrl}`);

    try {
      // Create a temporary directory for this session
      const tempDir = path.join(__dirname, 'temp', this.sessionId);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Use MediaRecorder input (WebM/MP4) for high-quality streaming
      const ffmpegArgs = [
        // Input: WebM or MP4 from MediaRecorder via stdin
        '-f', this.inputFormat || 'webm',
        '-i', 'pipe:0',

        // Video encoding - Twitch-compatible settings
        '-c:v', 'libx264',
        '-preset', 'fast',
        '-tune', 'zerolatency',
        '-profile:v', 'main',
        '-level', '4.0',
        '-pix_fmt', 'yuv420p',

        // Keyframe settings - Twitch requires keyframes every 2 seconds
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',
        '-force_key_frames', 'expr:gte(t,n_forced*2)',

        // Bitrate settings - conservative for Twitch
        '-b:v', '2000k',
        '-maxrate', '2500k',
        '-bufsize', '5000k',

        // Audio encoding
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-strict', 'experimental',

        // Output format - FLV for RTMP
        '-f', 'flv',

        // RTMP specific settings
        '-rtmp_live', 'live',
        '-rtmp_playpath', this.streamKey,

        // Add session info as text overlay
        '-vf', `drawtext=text='RTMP Test ${this.sessionId.substring(0,8)}':fontcolor=white:fontsize=24:x=10:y=10:box=1:boxcolor=black@0.5`,

        // Output URL
        this.fullRtmpUrl
      ];

      console.log(`FFmpeg command: ffmpeg ${ffmpegArgs.join(' ')}`);

      const { spawn } = require('child_process');
      this.ffmpegProcess = spawn('ffmpeg', ffmpegArgs, {
        stdio: ['pipe', 'pipe', 'pipe'] // stdin for video frames
      });

      this.isStreaming = true;
      this.startedAt = new Date();

      // Handle FFmpeg events
      this.ffmpegProcess.on('spawn', () => {
        console.log(`✅ FFmpeg process spawned for session ${this.sessionId}`);
        console.log(`🎥 Ready to receive ${this.inputFormat} chunks from MediaRecorder`);
      });

      this.ffmpegProcess.on('error', (error) => {
        console.error(`❌ FFmpeg process error for session ${this.sessionId}:`, error);
        this.isStreaming = false;
        this.cleanup();
      });

      this.ffmpegProcess.on('exit', (code, signal) => {
        console.log(`🏁 FFmpeg process exited for session ${this.sessionId} with code ${code}, signal ${signal}`);
        this.isStreaming = false;

        if (code !== 0) {
          console.error(`❌ FFmpeg failed with exit code ${code}`);
          if (code === 1) {
            console.error(`💡 This usually indicates an RTMP connection issue. Check your stream key and RTMP URL.`);
          }
        }
      });

      // Handle stderr for logging
      this.ffmpegProcess.stderr.on('data', (data) => {
        const message = data.toString();
        console.log(`FFmpeg [${this.sessionId}]: ${message.trim()}`);

        // Check for specific error patterns
        if (message.includes('Connection refused') || message.includes('Input/output error')) {
          console.error(`❌ RTMP connection failed. Possible issues:`);
          console.error(`   - Invalid stream key: ${this.streamKey}`);
          console.error(`   - RTMP URL incorrect: ${this.rtmpUrl}`);
          console.error(`   - Twitch server rejecting connection`);
          console.error(`   - Network connectivity issues`);
        }
      });

      // Handle stdout
      this.ffmpegProcess.stdout.on('data', (data) => {
        // FFmpeg stdout (usually empty for RTMP)
        console.log(`FFmpeg stdout [${this.sessionId}]: ${data.toString().trim()}`);
      });

    } catch (error) {
      console.error(`❌ Error starting FFmpeg for session ${this.sessionId}:`, error);
      this.isStreaming = false;
    }
  }

  // Old frame capture methods removed - using MediaRecorder chunks now

  // Method to receive video chunks from MediaRecorder
  receiveVideoChunk(chunkData, format) {
    try {
      // Start FFmpeg when we receive the first chunk
      if (!this.hasReceivedFirstChunk) {
        this.inputFormat = format;
        this.hasReceivedFirstChunk = true;
        console.log(`📋 First chunk received, format: ${format}`);
        console.log(`🚀 Starting FFmpeg with ${format} input...`);

        // Start FFmpeg now that we know the format
        this.startFFmpegStream();

        // Give FFmpeg a moment to start
        setTimeout(() => {
          this.processChunk(chunkData, format);
        }, 500);
        return;
      }

      this.processChunk(chunkData, format);
    } catch (error) {
      console.error(`❌ Error processing video chunk:`, error);
    }
  }

  processChunk(chunkData, format) {
    if (!this.ffmpegProcess || !this.ffmpegProcess.stdin || this.ffmpegProcess.stdin.destroyed) {
      console.log(`⚠️ Cannot write video chunk: FFmpeg process not ready`);
      return;
    }

    try {
      // Stop test frames when we start receiving real video data
      if (this.testFrameInterval) {
        clearInterval(this.testFrameInterval);
        this.testFrameInterval = null;
        console.log(`🎥 Switching from test frames to real ${format} video data`);
      }

      // Write video chunk directly to FFmpeg stdin
      const buffer = Buffer.from(chunkData);
      const written = this.ffmpegProcess.stdin.write(buffer);

      if (!written) {
        console.warn(`⚠️ FFmpeg stdin buffer is full, chunk may be dropped`);
      }

      this.chunkCount = (this.chunkCount || 0) + 1;
      this.totalBytes = (this.totalBytes || 0) + buffer.length;

      // Log progress
      const now = Date.now();
      if (!this.lastChunkTime) this.lastChunkTime = now;

      if (now - this.lastChunkTime > 1000) { // Every second
        const mbReceived = (this.totalBytes / (1024 * 1024)).toFixed(2);
        console.log(`📊 Processed ${this.chunkCount} ${format} chunks, ${mbReceived}MB total`);
        this.lastChunkTime = now;
        this.chunkCount = 0;
        this.totalBytes = 0;
      }
    } catch (error) {
      console.error(`❌ Error writing chunk to FFmpeg:`, error);
    }
  }

  // Legacy method - no longer used with MediaRecorder approach

  stop() {
    console.log(`🛑 Stopping RTMP session ${this.sessionId}`);
    this.cleanup();
  }

  cleanup() {
    console.log(`🧹 Cleaning up session ${this.sessionId}`);

    if (this.ffmpegProcess) {
      try {
        this.ffmpegProcess.kill('SIGTERM');

        // Force kill if it doesn't respond
        setTimeout(() => {
          if (this.ffmpegProcess && !this.ffmpegProcess.killed) {
            this.ffmpegProcess.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        console.error(`Error killing FFmpeg process:`, error);
      }
      this.ffmpegProcess = null;
    }

    this.isStreaming = false;

    // Clean up temp directory
    const tempDir = path.join(__dirname, 'temp', this.sessionId);
    if (fs.existsSync(tempDir)) {
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
      } catch (error) {
        console.error(`Error cleaning up temp directory:`, error);
      }
    }
  }

  getStatus() {
    return {
      sessionId: this.sessionId,
      rtmpUrl: this.rtmpUrl,
      isStreaming: this.isStreaming,
      startedAt: this.startedAt,
      frameCount: this.frameCount || 0,
      connectionState: 'websocket'
    };
  }
}

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);

  // Handle stream start request
  socket.on('start-stream', async ({ rtmpUrl, streamKey, roomId }) => {
    console.log(`🚀 Start stream request from ${socket.id} for room ${roomId}`);

    try {
      const sessionId = uuidv4();
      const session = new RTMPStreamingSession(sessionId, rtmpUrl, streamKey);
      streamingSessions.set(sessionId, session);

      // Initialize WebSocket-based streaming
      session.initializeStreaming();

      // Send session info back to client
      socket.emit('stream-session-created', {
        sessionId: sessionId,
        success: true
      });

      console.log(`✅ Stream session created: ${sessionId}`);

    } catch (error) {
      console.error(`❌ Error creating stream session:`, error);
      socket.emit('stream-session-created', {
        success: false,
        error: error.message
      });
    }
  });

  // WebRTC handlers removed - using WebSocket-based streaming

  // Handle video chunk data from MediaRecorder
  socket.on('video-chunk', ({ sessionId, data, format, timestamp }) => {
    // Only log every 10th chunk to avoid spam
    if (!socket.chunkLogCounter) socket.chunkLogCounter = 0;
    socket.chunkLogCounter++;

    if (socket.chunkLogCounter % 10 === 0) {
      const sizeKB = (data.byteLength / 1024).toFixed(1);
      console.log(`📹 Received ${format} chunk #${socket.chunkLogCounter} for session ${sessionId}, size: ${sizeKB}KB`);
    }

    const session = streamingSessions.get(sessionId);
    if (session) {
      try {
        session.receiveVideoChunk(data, format);
      } catch (error) {
        console.error(`❌ Error processing video chunk #${socket.chunkLogCounter}:`, error);
        // Don't disconnect on chunk errors
      }
    } else {
      console.error(`❌ Session ${sessionId} not found for video chunk`);
    }
  });

  // Handle stream stop
  socket.on('stop-stream', ({ sessionId }) => {
    console.log(`🛑 Stop stream request for session ${sessionId}`);

    const session = streamingSessions.get(sessionId);
    if (session) {
      session.stop();
      streamingSessions.delete(sessionId);
      socket.emit('stream-stopped', { sessionId, success: true });
    } else {
      socket.emit('stream-stopped', { sessionId, success: false, error: 'Session not found' });
    }
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
    // Note: In production, you might want to associate sessions with socket IDs
    // and clean up sessions when clients disconnect
  });
});

// REST API endpoints
app.get('/', (req, res) => {
  res.json({
    status: 'RTMP Streaming Service Running',
    timestamp: new Date().toISOString(),
    activeSessions: streamingSessions.size
  });
});

// Get all active sessions
app.get('/sessions', (req, res) => {
  const sessions = Array.from(streamingSessions.values()).map(session => session.getStatus());
  res.json({
    activeSessions: sessions.length,
    sessions: sessions
  });
});

// Get specific session status
app.get('/sessions/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = streamingSessions.get(sessionId);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  res.json(session.getStatus());
});

// Stop a specific session
app.delete('/sessions/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = streamingSessions.get(sessionId);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  session.stop();
  streamingSessions.delete(sessionId);

  res.json({ success: true, message: 'Session stopped' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    activeSessions: streamingSessions.size,
    memory: process.memoryUsage(),
    ffmpegAvailable: true // TODO: Check if FFmpeg is actually available
  });
});

const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
  console.log(`🚀 RTMP Streaming Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Sessions API: http://localhost:${PORT}/sessions`);

  // Create temp directory if it doesn't exist
  const tempDir = path.join(__dirname, 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  // Don't exit - try to continue running
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit - try to continue running
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');

  // Stop all active sessions
  streamingSessions.forEach((session, sessionId) => {
    console.log(`Stopping session ${sessionId}`);
    session.stop();
  });
  streamingSessions.clear();

  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');

  // Stop all active sessions
  streamingSessions.forEach((session, sessionId) => {
    console.log(`Stopping session ${sessionId}`);
    session.stop();
  });
  streamingSessions.clear();

  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
