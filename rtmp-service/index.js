const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const ffmpeg = require('fluent-ffmpeg');
const { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate } = require('wrtc');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: true,
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Store active streaming sessions
const streamingSessions = new Map();

class RTMPStreamingSession {
  constructor(sessionId, rtmpUrl, streamKey) {
    this.sessionId = sessionId;
    this.rtmpUrl = rtmpUrl;
    this.streamKey = streamKey;
    this.fullRtmpUrl = `${rtmpUrl}/${streamKey}`;
    this.ffmpegProcess = null;
    this.isStreaming = false;
    this.startedAt = null;

    // Validate RTMP URL format
    if (!rtmpUrl.startsWith('rtmp://')) {
      throw new Error(`Invalid RTMP URL format: ${rtmpUrl}. Must start with rtmp://`);
    }

    if (!streamKey || streamKey.trim().length === 0) {
      throw new Error('Stream key cannot be empty');
    }

    console.log(`📺 Created RTMP session ${sessionId} for ${this.fullRtmpUrl}`);
    console.log(`🔑 Stream key: ${streamKey.substring(0, 8)}...${streamKey.substring(streamKey.length - 4)}`);
  }

  // WebSocket-based streaming - no WebRTC peer connection needed
  initializeStreaming() {
    console.log(`🎥 Initializing WebSocket-based streaming for session ${this.sessionId}`);

    // Start FFmpeg immediately
    this.startFFmpegStream();

    return true;
  }

  // WebSocket-based streaming - no WebRTC offer/answer needed

  startFFmpegStream() {
    if (this.isStreaming) {
      return;
    }

    console.log(`🎬 Starting FFmpeg stream for session ${this.sessionId} to ${this.fullRtmpUrl}`);

    try {
      // Create a temporary directory for this session
      const tempDir = path.join(__dirname, 'temp', this.sessionId);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Use a more compatible FFmpeg configuration for Twitch
      const ffmpegArgs = [
        // Input: raw video frames from stdin
        '-f', 'rawvideo',
        '-pixel_format', 'rgba',
        '-video_size', '1920x1080',
        '-framerate', '30',
        '-i', 'pipe:0',

        // Generate audio tone
        '-f', 'lavfi',
        '-i', 'sine=frequency=1000:sample_rate=44100',

        // Video encoding - Twitch-compatible settings
        '-c:v', 'libx264',
        '-preset', 'fast',
        '-tune', 'zerolatency',
        '-profile:v', 'main',
        '-level', '4.0',
        '-pix_fmt', 'yuv420p',

        // Keyframe settings - Twitch requires keyframes every 2 seconds
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',
        '-force_key_frames', 'expr:gte(t,n_forced*2)',

        // Bitrate settings - conservative for Twitch
        '-b:v', '2000k',
        '-maxrate', '2500k',
        '-bufsize', '5000k',

        // Audio encoding
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        '-strict', 'experimental',

        // Output format - FLV for RTMP
        '-f', 'flv',

        // RTMP specific settings
        '-rtmp_live', 'live',
        '-rtmp_playpath', this.streamKey,

        // Add session info as text overlay
        '-vf', `drawtext=text='RTMP Test ${this.sessionId.substring(0,8)}':fontcolor=white:fontsize=24:x=10:y=10:box=1:boxcolor=black@0.5`,

        // Output URL
        this.fullRtmpUrl
      ];

      console.log(`FFmpeg command: ffmpeg ${ffmpegArgs.join(' ')}`);

      const { spawn } = require('child_process');
      this.ffmpegProcess = spawn('ffmpeg', ffmpegArgs, {
        stdio: ['pipe', 'pipe', 'pipe'] // stdin for video frames
      });

      this.isStreaming = true;
      this.startedAt = new Date();

      // Handle FFmpeg events
      this.ffmpegProcess.on('spawn', () => {
        console.log(`✅ FFmpeg process spawned for session ${this.sessionId}`);
        console.log(`🎥 Ready to receive video frames from WebSocket`);
        this.setupFrameCapture();
      });

      this.ffmpegProcess.on('error', (error) => {
        console.error(`❌ FFmpeg process error for session ${this.sessionId}:`, error);
        this.isStreaming = false;
        this.cleanup();
      });

      this.ffmpegProcess.on('exit', (code, signal) => {
        console.log(`🏁 FFmpeg process exited for session ${this.sessionId} with code ${code}, signal ${signal}`);
        this.isStreaming = false;

        if (code !== 0) {
          console.error(`❌ FFmpeg failed with exit code ${code}`);
          if (code === 1) {
            console.error(`💡 This usually indicates an RTMP connection issue. Check your stream key and RTMP URL.`);
          }
        }
      });

      // Handle stderr for logging
      this.ffmpegProcess.stderr.on('data', (data) => {
        const message = data.toString();
        console.log(`FFmpeg [${this.sessionId}]: ${message.trim()}`);

        // Check for specific error patterns
        if (message.includes('Connection refused') || message.includes('Input/output error')) {
          console.error(`❌ RTMP connection failed. Possible issues:`);
          console.error(`   - Invalid stream key: ${this.streamKey}`);
          console.error(`   - RTMP URL incorrect: ${this.rtmpUrl}`);
          console.error(`   - Twitch server rejecting connection`);
          console.error(`   - Network connectivity issues`);
        }
      });

      // Handle stdout
      this.ffmpegProcess.stdout.on('data', (data) => {
        // FFmpeg stdout (usually empty for RTMP)
        console.log(`FFmpeg stdout [${this.sessionId}]: ${data.toString().trim()}`);
      });

    } catch (error) {
      console.error(`❌ Error starting FFmpeg for session ${this.sessionId}:`, error);
      this.isStreaming = false;
    }
  }

  setupFrameCapture() {
    console.log(`🎬 Setting up frame capture for session ${this.sessionId}`);

    // Set up frame reception via WebSocket
    this.frameCount = 0;
    this.lastFrameTime = Date.now();

    // Start sending test frames until we receive real frames
    this.sendTestFrames();
  }

  sendTestFrames() {
    if (!this.ffmpegProcess || !this.ffmpegProcess.stdin || this.ffmpegProcess.stdin.destroyed) {
      return;
    }

    console.log(`🎨 Sending test frames while waiting for real video data`);

    // Create a simple colored frame (1920x1080 RGBA)
    const width = 1920;
    const height = 1080;
    const frameSize = width * height * 4; // RGBA
    const testFrame = Buffer.alloc(frameSize);

    // Fill with a gradient pattern
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const offset = (y * width + x) * 4;
        testFrame[offset] = Math.floor((x / width) * 255);     // R
        testFrame[offset + 1] = Math.floor((y / height) * 255); // G
        testFrame[offset + 2] = 128;                            // B
        testFrame[offset + 3] = 255;                            // A
      }
    }

    // Send frames at 30 FPS
    this.testFrameInterval = setInterval(() => {
      if (this.ffmpegProcess && this.ffmpegProcess.stdin && !this.ffmpegProcess.stdin.destroyed) {
        try {
          this.ffmpegProcess.stdin.write(testFrame);
          this.frameCount++;

          // Log progress every 30 frames (1 second)
          if (this.frameCount % 30 === 0) {
            console.log(`📊 Sent ${this.frameCount} test frames to FFmpeg`);
          }
        } catch (error) {
          console.error(`❌ Error writing test frame:`, error);
          clearInterval(this.testFrameInterval);
        }
      } else {
        clearInterval(this.testFrameInterval);
      }
    }, 33); // ~30 FPS (1000ms / 30 = 33.33ms)
  }

  // Method to receive JPEG frames from WebSocket (more efficient)
  receiveJPEGFrame(base64Data) {
    try {
      // Stop test frames when we start receiving real frames
      if (this.testFrameInterval) {
        clearInterval(this.testFrameInterval);
        this.testFrameInterval = null;
        console.log(`🎥 Switching from test frames to real JPEG data`);

        // Switch FFmpeg to accept JPEG input instead of raw video
        this.restartFFmpegForJPEG();
      }

      // For now, just log that we're receiving JPEG frames
      // TODO: Implement JPEG to raw video conversion for FFmpeg
      this.frameCount = (this.frameCount || 0) + 1;

      // Log progress
      const now = Date.now();
      if (!this.lastFrameTime) this.lastFrameTime = now;

      if (now - this.lastFrameTime > 1000) { // Every second
        console.log(`📊 Processed ${this.frameCount} JPEG frames`);
        this.lastFrameTime = now;
        this.frameCount = 0;
      }
    } catch (error) {
      console.error(`❌ Error processing JPEG frame:`, error);
    }
  }

  // Method to receive raw video frames from WebSocket (legacy)
  receiveVideoFrame(frameData) {
    if (!this.ffmpegProcess || !this.ffmpegProcess.stdin || this.ffmpegProcess.stdin.destroyed) {
      console.log(`⚠️ Cannot write frame: FFmpeg process not ready`);
      return;
    }

    try {
      // Stop test frames when we start receiving real frames
      if (this.testFrameInterval) {
        clearInterval(this.testFrameInterval);
        this.testFrameInterval = null;
        console.log(`🎥 Switching from test frames to real video data`);
      }

      // Validate frame data size (1920x1080x4 = 8,294,400 bytes)
      const expectedSize = 1920 * 1080 * 4;
      if (frameData.length !== expectedSize) {
        console.error(`❌ Invalid frame size: ${frameData.length}, expected: ${expectedSize}`);
        return;
      }

      // Write the real frame data to FFmpeg
      const written = this.ffmpegProcess.stdin.write(frameData);
      if (!written) {
        console.warn(`⚠️ FFmpeg stdin buffer is full, frame may be dropped`);
      }

      this.frameCount = (this.frameCount || 0) + 1;

      // Log progress
      const now = Date.now();
      if (!this.lastFrameTime) this.lastFrameTime = now;

      if (now - this.lastFrameTime > 1000) { // Every second
        console.log(`📊 Processed ${this.frameCount} real video frames`);
        this.lastFrameTime = now;
        this.frameCount = 0;
      }
    } catch (error) {
      console.error(`❌ Error writing video frame:`, error);
      // Don't crash the session on frame errors
    }
  }

  restartFFmpegForJPEG() {
    // For now, just continue with the current FFmpeg process
    // In a full implementation, you would restart FFmpeg with JPEG input
    console.log(`📝 Note: JPEG input received, but continuing with current FFmpeg process`);
    console.log(`💡 Future enhancement: Restart FFmpeg to accept JPEG input instead of raw video`);
  }

  stop() {
    console.log(`🛑 Stopping RTMP session ${this.sessionId}`);
    this.cleanup();
  }

  cleanup() {
    console.log(`🧹 Cleaning up session ${this.sessionId}`);

    // Stop test frame generation
    if (this.testFrameInterval) {
      clearInterval(this.testFrameInterval);
      this.testFrameInterval = null;
    }

    if (this.ffmpegProcess) {
      try {
        this.ffmpegProcess.kill('SIGTERM');

        // Force kill if it doesn't respond
        setTimeout(() => {
          if (this.ffmpegProcess && !this.ffmpegProcess.killed) {
            this.ffmpegProcess.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        console.error(`Error killing FFmpeg process:`, error);
      }
      this.ffmpegProcess = null;
    }

    this.isStreaming = false;

    // Clean up temp directory
    const tempDir = path.join(__dirname, 'temp', this.sessionId);
    if (fs.existsSync(tempDir)) {
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
      } catch (error) {
        console.error(`Error cleaning up temp directory:`, error);
      }
    }
  }

  getStatus() {
    return {
      sessionId: this.sessionId,
      rtmpUrl: this.rtmpUrl,
      isStreaming: this.isStreaming,
      startedAt: this.startedAt,
      frameCount: this.frameCount || 0,
      connectionState: 'websocket'
    };
  }
}

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);

  // Handle stream start request
  socket.on('start-stream', async ({ rtmpUrl, streamKey, roomId }) => {
    console.log(`🚀 Start stream request from ${socket.id} for room ${roomId}`);

    try {
      const sessionId = uuidv4();
      const session = new RTMPStreamingSession(sessionId, rtmpUrl, streamKey);
      streamingSessions.set(sessionId, session);

      // Initialize WebSocket-based streaming
      session.initializeStreaming();

      // Send session info back to client
      socket.emit('stream-session-created', {
        sessionId: sessionId,
        success: true
      });

      console.log(`✅ Stream session created: ${sessionId}`);

    } catch (error) {
      console.error(`❌ Error creating stream session:`, error);
      socket.emit('stream-session-created', {
        success: false,
        error: error.message
      });
    }
  });

  // WebRTC handlers removed - using WebSocket-based streaming

  // Handle video frame data
  socket.on('video-frame', ({ sessionId, frameData, format }) => {
    // Only log every 30th frame to avoid spam
    if (!socket.frameLogCounter) socket.frameLogCounter = 0;
    socket.frameLogCounter++;

    if (socket.frameLogCounter % 30 === 0) {
      console.log(`📹 Received ${format || 'raw'} frame #${socket.frameLogCounter} for session ${sessionId}`);
    }

    const session = streamingSessions.get(sessionId);
    if (session) {
      try {
        if (format === 'jpeg') {
          // Handle base64 JPEG data
          session.receiveJPEGFrame(frameData);
        } else {
          // Handle raw RGBA data (legacy)
          const buffer = Buffer.from(frameData);
          session.receiveVideoFrame(buffer);
        }
      } catch (error) {
        console.error(`❌ Error processing video frame #${socket.frameLogCounter}:`, error);
        // Don't disconnect on frame errors
      }
    } else {
      console.error(`❌ Session ${sessionId} not found for video frame`);
    }
  });

  // Handle stream stop
  socket.on('stop-stream', ({ sessionId }) => {
    console.log(`🛑 Stop stream request for session ${sessionId}`);

    const session = streamingSessions.get(sessionId);
    if (session) {
      session.stop();
      streamingSessions.delete(sessionId);
      socket.emit('stream-stopped', { sessionId, success: true });
    } else {
      socket.emit('stream-stopped', { sessionId, success: false, error: 'Session not found' });
    }
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
    // Note: In production, you might want to associate sessions with socket IDs
    // and clean up sessions when clients disconnect
  });
});

// REST API endpoints
app.get('/', (req, res) => {
  res.json({
    status: 'RTMP Streaming Service Running',
    timestamp: new Date().toISOString(),
    activeSessions: streamingSessions.size
  });
});

// Get all active sessions
app.get('/sessions', (req, res) => {
  const sessions = Array.from(streamingSessions.values()).map(session => session.getStatus());
  res.json({
    activeSessions: sessions.length,
    sessions: sessions
  });
});

// Get specific session status
app.get('/sessions/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = streamingSessions.get(sessionId);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  res.json(session.getStatus());
});

// Stop a specific session
app.delete('/sessions/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = streamingSessions.get(sessionId);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  session.stop();
  streamingSessions.delete(sessionId);

  res.json({ success: true, message: 'Session stopped' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    activeSessions: streamingSessions.size,
    memory: process.memoryUsage(),
    ffmpegAvailable: true // TODO: Check if FFmpeg is actually available
  });
});

const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
  console.log(`🚀 RTMP Streaming Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Sessions API: http://localhost:${PORT}/sessions`);

  // Create temp directory if it doesn't exist
  const tempDir = path.join(__dirname, 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  // Don't exit - try to continue running
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit - try to continue running
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');

  // Stop all active sessions
  streamingSessions.forEach((session, sessionId) => {
    console.log(`Stopping session ${sessionId}`);
    session.stop();
  });
  streamingSessions.clear();

  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');

  // Stop all active sessions
  streamingSessions.forEach((session, sessionId) => {
    console.log(`Stopping session ${sessionId}`);
    session.stop();
  });
  streamingSessions.clear();

  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
