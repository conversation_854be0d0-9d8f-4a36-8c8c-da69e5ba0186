const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const ffmpeg = require('fluent-ffmpeg');
const { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate } = require('wrtc');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: true,
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Store active streaming sessions
const streamingSessions = new Map();

class RTMPStreamingSession {
  constructor(sessionId, rtmpUrl, streamKey) {
    this.sessionId = sessionId;
    this.rtmpUrl = rtmpUrl;
    this.streamKey = streamKey;
    this.fullRtmpUrl = `${rtmpUrl}/${streamKey}`;
    this.peerConnection = null;
    this.ffmpegProcess = null;
    this.isStreaming = false;
    this.startedAt = null;
    this.videoTrack = null;
    this.audioTrack = null;
    
    console.log(`📺 Created RTMP session ${sessionId} for ${this.fullRtmpUrl}`);
  }

  async createPeerConnection() {
    console.log(`🔗 Creating peer connection for session ${this.sessionId}`);
    
    // Configure ICE servers (use environment variables or defaults)
    const iceServers = [];
    
    // Add STUN server
    iceServers.push({ urls: 'stun:stun.l.google.com:19302' });
    
    // Add TURN server if configured
    const turnServer = process.env.TURN_SERVER;
    const turnUsername = process.env.TURN_USERNAME || 'webrtc';
    const turnPassword = process.env.TURN_PASSWORD || 'webrtc123';
    
    if (turnServer) {
      iceServers.push({
        urls: [`turn:${turnServer}:3478`],
        username: turnUsername,
        credential: turnPassword
      });
    }

    this.peerConnection = new RTCPeerConnection({
      iceServers: iceServers
    });

    // Handle incoming tracks
    this.peerConnection.ontrack = (event) => {
      console.log(`📹 Received ${event.track.kind} track for session ${this.sessionId}`);
      
      if (event.track.kind === 'video') {
        this.videoTrack = event.track;
      } else if (event.track.kind === 'audio') {
        this.audioTrack = event.track;
      }

      // Start FFmpeg when we have both tracks or just video
      if (this.videoTrack && !this.isStreaming) {
        this.startFFmpegStream();
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      console.log(`🔄 Connection state changed to: ${this.peerConnection.connectionState} for session ${this.sessionId}`);
      
      if (this.peerConnection.connectionState === 'failed' || 
          this.peerConnection.connectionState === 'disconnected') {
        this.cleanup();
      }
    };

    // Handle ICE connection state changes
    this.peerConnection.oniceconnectionstatechange = () => {
      console.log(`🧊 ICE connection state: ${this.peerConnection.iceConnectionState} for session ${this.sessionId}`);
    };

    return this.peerConnection;
  }

  async handleOffer(offer) {
    console.log(`📨 Handling offer for session ${this.sessionId}`);
    
    if (!this.peerConnection) {
      await this.createPeerConnection();
    }

    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);

    console.log(`📤 Created answer for session ${this.sessionId}`);
    return answer;
  }

  async handleIceCandidate(candidate) {
    if (this.peerConnection && candidate) {
      try {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        console.log(`🧊 Added ICE candidate for session ${this.sessionId}`);
      } catch (error) {
        console.error(`❌ Error adding ICE candidate for session ${this.sessionId}:`, error);
      }
    }
  }

  startFFmpegStream() {
    if (this.isStreaming || !this.videoTrack) {
      return;
    }

    console.log(`🎬 Starting FFmpeg stream for session ${this.sessionId} to ${this.fullRtmpUrl}`);

    try {
      // Create a temporary directory for this session
      const tempDir = path.join(__dirname, 'temp', this.sessionId);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // For now, use a test pattern to verify the RTMP pipeline works
      // This will help us confirm that FFmpeg can connect to Twitch
      const ffmpegArgs = [
        // Generate a test pattern (color bars with timer)
        '-f', 'lavfi',
        '-i', 'testsrc2=size=1920x1080:rate=30,format=yuv420p',
        '-f', 'lavfi',
        '-i', 'sine=frequency=1000:sample_rate=44100',

        // Video encoding
        '-c:v', 'libx264',
        '-preset', 'veryfast',
        '-tune', 'zerolatency',
        '-profile:v', 'baseline',
        '-level', '3.0',
        '-pix_fmt', 'yuv420p',

        // Keyframe settings
        '-g', '60',
        '-keyint_min', '60',
        '-sc_threshold', '0',

        // Bitrate settings
        '-b:v', '2500k',
        '-maxrate', '2500k',
        '-bufsize', '5000k',

        // Audio encoding
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',

        // Output format
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',

        // RTMP specific
        '-rtmp_live', 'live',
        '-rtmp_buffer', '1000',

        // Add session info as text overlay
        '-vf', `drawtext=text='Session ${this.sessionId.substring(0,8)}':fontcolor=white:fontsize=24:x=10:y=10`,

        // Output URL
        this.fullRtmpUrl
      ];

      console.log(`FFmpeg command: ffmpeg ${ffmpegArgs.join(' ')}`);

      const { spawn } = require('child_process');
      this.ffmpegProcess = spawn('ffmpeg', ffmpegArgs, {
        stdio: ['ignore', 'pipe', 'pipe'] // No stdin needed for test pattern
      });

      this.isStreaming = true;
      this.startedAt = new Date();

      // Handle FFmpeg events
      this.ffmpegProcess.on('spawn', () => {
        console.log(`✅ FFmpeg process spawned for session ${this.sessionId}`);
        console.log(`🎨 Streaming test pattern to verify RTMP connection`);
      });

      this.ffmpegProcess.on('error', (error) => {
        console.error(`❌ FFmpeg process error for session ${this.sessionId}:`, error);
        this.isStreaming = false;
        this.cleanup();
      });

      this.ffmpegProcess.on('exit', (code, signal) => {
        console.log(`🏁 FFmpeg process exited for session ${this.sessionId} with code ${code}, signal ${signal}`);
        this.isStreaming = false;
      });

      // Handle stderr for logging
      this.ffmpegProcess.stderr.on('data', (data) => {
        const message = data.toString();
        // Log important messages
        if (message.includes('error') || message.includes('warning') ||
            message.includes('fps=') || message.includes('bitrate=')) {
          console.log(`FFmpeg [${this.sessionId}]: ${message.trim()}`);
        }
      });

    } catch (error) {
      console.error(`❌ Error starting FFmpeg for session ${this.sessionId}:`, error);
      this.isStreaming = false;
    }
  }

  // Note: This is currently using a test pattern to verify the RTMP pipeline
  // In a production implementation, you would need to:
  // 1. Capture frames from the WebRTC video track using a library like node-canvas
  // 2. Convert the frames to raw video format
  // 3. Feed them to FFmpeg stdin
  // 4. Handle audio track data similarly

  stop() {
    console.log(`🛑 Stopping RTMP session ${this.sessionId}`);
    this.cleanup();
  }

  cleanup() {
    console.log(`🧹 Cleaning up session ${this.sessionId}`);

    if (this.ffmpegProcess) {
      try {
        this.ffmpegProcess.kill('SIGTERM');

        // Force kill if it doesn't respond
        setTimeout(() => {
          if (this.ffmpegProcess && !this.ffmpegProcess.killed) {
            this.ffmpegProcess.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        console.error(`Error killing FFmpeg process:`, error);
      }
      this.ffmpegProcess = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.isStreaming = false;
    this.videoTrack = null;
    this.audioTrack = null;

    // Clean up temp directory
    const tempDir = path.join(__dirname, 'temp', this.sessionId);
    if (fs.existsSync(tempDir)) {
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
      } catch (error) {
        console.error(`Error cleaning up temp directory:`, error);
      }
    }
  }

  getStatus() {
    return {
      sessionId: this.sessionId,
      rtmpUrl: this.rtmpUrl,
      isStreaming: this.isStreaming,
      startedAt: this.startedAt,
      connectionState: this.peerConnection?.connectionState || 'disconnected',
      iceConnectionState: this.peerConnection?.iceConnectionState || 'disconnected',
      hasVideo: !!this.videoTrack,
      hasAudio: !!this.audioTrack
    };
  }
}

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);

  // Handle stream start request
  socket.on('start-stream', async ({ rtmpUrl, streamKey, roomId }) => {
    console.log(`🚀 Start stream request from ${socket.id} for room ${roomId}`);
    
    try {
      const sessionId = uuidv4();
      const session = new RTMPStreamingSession(sessionId, rtmpUrl, streamKey);
      streamingSessions.set(sessionId, session);
      
      // Create peer connection
      await session.createPeerConnection();
      
      // Send session info back to client
      socket.emit('stream-session-created', {
        sessionId: sessionId,
        success: true
      });
      
      console.log(`✅ Stream session created: ${sessionId}`);
      
    } catch (error) {
      console.error(`❌ Error creating stream session:`, error);
      socket.emit('stream-session-created', {
        success: false,
        error: error.message
      });
    }
  });

  // Handle WebRTC offer
  socket.on('webrtc-offer', async ({ sessionId, offer }) => {
    console.log(`📨 Received WebRTC offer for session ${sessionId}`);
    
    const session = streamingSessions.get(sessionId);
    if (!session) {
      socket.emit('webrtc-error', { sessionId, error: 'Session not found' });
      return;
    }

    try {
      const answer = await session.handleOffer(offer);
      socket.emit('webrtc-answer', { sessionId, answer });
    } catch (error) {
      console.error(`❌ Error handling offer for session ${sessionId}:`, error);
      socket.emit('webrtc-error', { sessionId, error: error.message });
    }
  });

  // Handle ICE candidates
  socket.on('webrtc-ice-candidate', async ({ sessionId, candidate }) => {
    const session = streamingSessions.get(sessionId);
    if (session) {
      await session.handleIceCandidate(candidate);
    }
  });

  // Handle stream stop
  socket.on('stop-stream', ({ sessionId }) => {
    console.log(`🛑 Stop stream request for session ${sessionId}`);
    
    const session = streamingSessions.get(sessionId);
    if (session) {
      session.stop();
      streamingSessions.delete(sessionId);
      socket.emit('stream-stopped', { sessionId, success: true });
    } else {
      socket.emit('stream-stopped', { sessionId, success: false, error: 'Session not found' });
    }
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
    // Note: In production, you might want to associate sessions with socket IDs
    // and clean up sessions when clients disconnect
  });
});

// REST API endpoints
app.get('/', (req, res) => {
  res.json({
    status: 'RTMP Streaming Service Running',
    timestamp: new Date().toISOString(),
    activeSessions: streamingSessions.size
  });
});

// Get all active sessions
app.get('/sessions', (req, res) => {
  const sessions = Array.from(streamingSessions.values()).map(session => session.getStatus());
  res.json({
    activeSessions: sessions.length,
    sessions: sessions
  });
});

// Get specific session status
app.get('/sessions/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = streamingSessions.get(sessionId);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  res.json(session.getStatus());
});

// Stop a specific session
app.delete('/sessions/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = streamingSessions.get(sessionId);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  session.stop();
  streamingSessions.delete(sessionId);

  res.json({ success: true, message: 'Session stopped' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    activeSessions: streamingSessions.size,
    memory: process.memoryUsage(),
    ffmpegAvailable: true // TODO: Check if FFmpeg is actually available
  });
});

const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
  console.log(`🚀 RTMP Streaming Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Sessions API: http://localhost:${PORT}/sessions`);

  // Create temp directory if it doesn't exist
  const tempDir = path.join(__dirname, 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');

  // Stop all active sessions
  streamingSessions.forEach((session, sessionId) => {
    console.log(`Stopping session ${sessionId}`);
    session.stop();
  });
  streamingSessions.clear();

  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');

  // Stop all active sessions
  streamingSessions.forEach((session, sessionId) => {
    console.log(`Stopping session ${sessionId}`);
    session.stop();
  });
  streamingSessions.clear();

  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
