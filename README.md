# Switcher.AI - Professional WebRTC Streaming Platform

A production-ready multi-peer WebRTC streaming application with WebGL compositing and RTMP output capabilities.

## Features

### Core Functionality
- **Multi-peer WebRTC Streaming**: Real-time video and audio streaming between multiple participants
- **WebGL Video Compositing**: Hardware-accelerated video processing for combining multiple feeds
- **RTMP Output**: Stream composite video to external platforms (Twitch, YouTube, etc.)
- **Real-time Audio Mixing**: Synchronized audio from multiple sources
- **Professional UI**: Broadcast-quality interface with modern design

### Host Features
- Create and manage streaming rooms
- Composite multiple video feeds with different layouts (Grid, Focus, PIP)
- Real-time participant management
- RTMP streaming configuration
- Connection quality monitoring

### Participant Features
- Join rooms with simple room codes
- Camera and microphone controls
- Real-time connection status
- Low-latency streaming to host

## Tech Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **WebRTC** for peer-to-peer connections
- **WebGL Canvas** for video compositing
- **Socket.io Client** for signaling

### Backend
- **Node.js** with Express
- **Socket.io** for WebRTC signaling
- **Separate RTMP Service** with FFmpeg for high-performance streaming

## Getting Started

### Prerequisites
- Node.js 16+ 
- Modern browser with WebRTC support
- Camera and microphone access

### Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Start the development servers:
```bash
npm run dev
```

This will start both the client (Vite) on port 5173 and the signaling server on port 3001.

### Production Build & Deployment

The application now serves the frontend through the Express server for simplified deployment.

#### Local Production Testing
```bash
npm run build:serve
```

This builds the frontend and starts the server serving both the website and API on port 3001.

#### Deploy to Google Cloud Run
```bash
npm run deploy
```

Or manually:
```bash
npm run build
gcloud run deploy poc-webrtc --source . --platform managed --region us-central1 --allow-unauthenticated
```

**Note**: The deployment now happens from the project root, not the server folder, since the server needs access to the built frontend files.

### Usage

1. **Create a Room**: Click "Create Room" to start as a host
2. **Join a Room**: Enter a room ID to join as a participant
3. **Configure Streaming**: Hosts can set up RTMP streaming in the settings panel
4. **Manage Layout**: Choose between Grid, Focus, or Picture-in-Picture layouts

## Architecture

### WebRTC Flow
1. Participants join rooms via the signaling server
2. Peer connections are established using ICE candidates
3. Media streams are exchanged directly between peers
4. Host receives all participant streams for compositing

### Video Compositing
- Uses HTML5 Canvas with 2D context for real-time compositing
- Supports multiple layout modes with smooth transitions
- Optimized for 1920x1080 output resolution
- Hardware-accelerated rendering when available

### RTMP Integration
The application includes a complete RTMP streaming solution:
- **Separate RTMP Service**: Dockerized microservice for WebRTC to RTMP conversion
- **FFmpeg Integration**: High-performance video encoding and streaming
- **WebRTC Bridge**: Direct peer-to-peer connection from host to RTMP service
- **Real-time Processing**: Low-latency composite video streaming

**RTMP Streaming Flow:**
1. Host creates composite video using WebGL
2. Canvas stream is captured at 30 FPS
3. WebRTC connection established to RTMP service
4. FFmpeg processes and streams to RTMP endpoint
5. Real-time monitoring and status updates

## Configuration

### Environment Variables
- `PORT`: Server port (default: 3001)
- `VITE_TURN_SERVER`: Custom TURN server IP/domain (optional)
- `VITE_TURN_USERNAME`: TURN server username (default: webrtc)
- `VITE_TURN_PASSWORD`: TURN server password (default: webrtc123)
- `VITE_RTMP_SERVICE_URL`: RTMP service URL (default: http://localhost:3002)
- `RTMP_SERVICE_URL`: Server-side RTMP service URL

### TURN Server Setup

For reliable WebRTC connections across different networks, deploy your own TURN server:

```bash
# Deploy TURN server to Google Cloud
cd turn-server
./deploy-gce.sh

# Configure client to use deployed server
./configure-client.sh
```

See `turn-server/README.md` for detailed TURN server documentation.

### RTMP Setup
Configure your streaming platform's RTMP settings:
- **Twitch**: `rtmp://live.twitch.tv/live/` + stream key
- **YouTube**: `rtmp://a.rtmp.youtube.com/live2/` + stream key
- **Custom**: Your RTMP server URL + stream key

## Development

### Project Structure
```
src/
├── components/          # React components
├── contexts/           # React contexts (Connection management)
├── App.tsx            # Main application component
└── main.tsx           # Application entry point

server/
├── index.js           # WebRTC signaling server
└── package.json       # Server dependencies

turn-server/
├── Dockerfile         # Coturn TURN server container
├── turnserver.conf    # Coturn configuration
├── deploy-gce.sh      # GCE deployment script
├── configure-client.sh # Client configuration script
├── docker-compose.yml # Local development setup
└── README.md          # TURN server documentation
```

### Key Components
- **WelcomeScreen**: Room creation and joining interface
- **HostInterface**: Main hosting dashboard with compositing
- **ParticipantInterface**: Participant streaming interface
- **VideoCompositor**: WebGL-based video compositing engine
- **ConnectionContext**: WebRTC connection management

## Production Deployment

The application is now configured as a single deployable unit where the Express server serves both the API and the built frontend.

### Google Cloud Run (Recommended)
```bash
npm run deploy
```

### Other Platforms
1. Build the frontend: `npm run build`
2. Deploy the entire project (the server will serve static files from `dist/`)
3. Configure HTTPS (required for WebRTC)
4. Set environment variables:
   - `PORT`: Server port
   - Configure TURN servers for NAT traversal

### Manual Deployment
- Deploy Node.js server with process manager (PM2)
- Ensure `dist/` folder is included in deployment
- Set up SSL certificates
- Scale with load balancer if needed

### RTMP Server
- Implement FFmpeg processing pipeline
- Add stream authentication and monitoring
- Configure CDN integration for global distribution

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

WebRTC and Canvas API support required.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details.