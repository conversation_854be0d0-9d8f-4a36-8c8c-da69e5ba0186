import { io, Socket } from 'socket.io-client';

interface RTMPServiceConfig {
  url: string;
  streamKey: string;
}

interface RTMPSession {
  sessionId: string;
  isConnected: boolean;
  isStreaming: boolean;
}

export class RTMPService {
  private socket: Socket | null = null;
  private peerConnection: RTCPeerConnection | null = null;
  private session: RTMPSession | null = null;
  private serviceUrl: string;
  private onStatusChange?: (status: { streaming: boolean; message: string }) => void;

  constructor(serviceUrl: string) {
    this.serviceUrl = serviceUrl;
  }

  /**
   * Set callback for status changes
   */
  setStatusCallback(callback: (status: { streaming: boolean; message: string }) => void) {
    this.onStatusChange = callback;
  }

  /**
   * Connect to the RTMP service
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) {
      console.log('🔌 Already connected to RTMP service');
      return;
    }

    console.log(`🔌 Connecting to RTMP service at ${this.serviceUrl}`);

    this.socket = io(this.serviceUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000
    });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('RTMP service connection timeout'));
      }, 10000);

      this.socket!.on('connect', () => {
        clearTimeout(timeout);
        console.log('✅ Connected to RTMP service');
        console.log(`🆔 Socket ID: ${this.socket!.id}`);
        resolve();
      });

      this.socket!.on('connect_error', (error) => {
        clearTimeout(timeout);
        console.error('❌ RTMP service connection error:', error);
        reject(new Error(`RTMP service connection failed: ${error.message}`));
      });

      this.socket!.on('disconnect', (reason) => {
        console.log(`🔌 Disconnected from RTMP service: ${reason}`);
        this.cleanup();
      });

      // Add debugging for all events
      this.socket!.onAny((eventName, ...args) => {
        console.log(`📡 Socket event: ${eventName}`, args);
      });
    });
  }

  /**
   * Start streaming to RTMP endpoint
   */
  async startStreaming(config: RTMPServiceConfig): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('Not connected to RTMP service');
    }

    console.log(`🚀 Starting RTMP stream to ${config.url}`);

    // Request stream session
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Stream session creation timeout'));
      }, 15000);

      this.socket!.emit('start-stream', {
        rtmpUrl: config.url,
        streamKey: config.streamKey,
        roomId: 'host-stream' // Could be dynamic based on room
      });

      this.socket!.on('stream-session-created', ({ sessionId, success, error }) => {
        clearTimeout(timeout);
        
        if (success) {
          this.session = {
            sessionId,
            isConnected: false,
            isStreaming: false
          };
          console.log(`✅ RTMP session created: ${sessionId}`);
          resolve();
        } else {
          console.error(`❌ Failed to create RTMP session: ${error}`);
          reject(new Error(`Failed to create RTMP session: ${error}`));
        }
      });
    });
  }

  /**
   * Setup video frame streaming via WebSocket
   */
  async setupVideoStreaming(canvas: HTMLCanvasElement): Promise<void> {
    if (!this.session) {
      throw new Error('No active RTMP session');
    }

    console.log(`🎥 Setting up video streaming for session ${this.session.sessionId}`);
    console.log(`📐 Canvas dimensions: ${canvas.width}x${canvas.height}`);

    // Verify canvas is ready
    if (canvas.width === 0 || canvas.height === 0) {
      throw new Error('Canvas has invalid dimensions');
    }

    // Start capturing and sending frames
    this.startFrameCapture(canvas);

    // Update session state
    if (this.session) {
      this.session.isConnected = true;
      this.session.isStreaming = true;
    }

    // Notify status change
    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: true,
        message: 'Streaming video frames to RTMP service'
      });
    }
  }

  /**
   * Start capturing and sending video frames
   */
  private startFrameCapture(canvas: HTMLCanvasElement): void {
    if (!this.socket || !this.session) {
      console.error('❌ Cannot start frame capture: missing socket or session');
      return;
    }

    console.log(`📹 Starting frame capture at 30 FPS`);
    console.log(`🔌 Socket connected: ${this.socket.connected}`);
    console.log(`📺 Session ID: ${this.session.sessionId}`);

    // Create a separate 2D canvas for frame capture
    const captureCanvas = document.createElement('canvas');
    captureCanvas.width = canvas.width;
    captureCanvas.height = canvas.height;
    const captureCtx = captureCanvas.getContext('2d');

    if (!captureCtx) {
      throw new Error('Could not create capture canvas 2D context');
    }

    console.log(`📐 Capture canvas created: ${captureCanvas.width}x${captureCanvas.height}`);

    let frameCount = 0;
    let lastLogTime = Date.now();
    let totalFramesSent = 0;

    const captureFrame = () => {
      if (!this.socket || !this.session || !this.session.isStreaming) {
        console.log(`⏹️ Stopping frame capture: socket=${!!this.socket}, session=${!!this.session}, streaming=${this.session?.isStreaming}`);
        return;
      }

      try {
        // Copy the WebGL canvas to our 2D canvas
        captureCtx.drawImage(canvas, 0, 0);

        // Get image data from the 2D canvas
        const imageData = captureCtx.getImageData(0, 0, captureCanvas.width, captureCanvas.height);

        // Convert to Uint8Array (RGBA format) - more efficient than Buffer
        const frameData = new Uint8Array(imageData.data);

        // Send frame to RTMP service
        this.socket.emit('video-frame', {
          sessionId: this.session.sessionId,
          frameData: Array.from(frameData) // Convert to regular array for JSON serialization
        });

        frameCount++;
        totalFramesSent++;

        // Log progress every second
        const now = Date.now();
        if (now - lastLogTime > 1000) {
          console.log(`📊 Sent ${frameCount} frames to RTMP service (total: ${totalFramesSent})`);
          lastLogTime = now;
          frameCount = 0;
        }

        // Schedule next frame
        if (this.session.isStreaming) {
          setTimeout(captureFrame, 33); // ~30 FPS
        }
      } catch (error) {
        console.error('❌ Error capturing frame:', error);
        if (this.onStatusChange) {
          this.onStatusChange({
            streaming: false,
            message: `Frame capture error: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
        }
      }
    };

    // Start frame capture after a small delay to ensure everything is ready
    console.log(`🚀 Starting frame capture in 100ms...`);
    setTimeout(() => {
      console.log(`▶️ Frame capture started!`);
      captureFrame();
    }, 100);
  }

  /**
   * Stop streaming
   */
  async stopStreaming(): Promise<void> {
    if (!this.session || !this.socket) {
      return;
    }

    console.log(`🛑 Stopping RTMP stream for session ${this.session.sessionId}`);

    this.socket.emit('stop-stream', {
      sessionId: this.session.sessionId
    });

    // Wait for confirmation
    return new Promise((resolve) => {
      const timeout = setTimeout(resolve, 5000); // 5 second timeout

      this.socket!.on('stream-stopped', ({ sessionId, success, error }) => {
        if (sessionId === this.session!.sessionId) {
          clearTimeout(timeout);
          if (success) {
            console.log(`✅ RTMP stream stopped successfully`);
          } else {
            console.error(`❌ Error stopping RTMP stream: ${error}`);
          }
          this.cleanup();
          resolve();
        }
      });
    });
  }

  /**
   * Disconnect from service
   */
  disconnect(): void {
    console.log('🔌 Disconnecting from RTMP service');
    this.cleanup();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Get current session status
   */
  getStatus(): RTMPSession | null {
    return this.session;
  }

  /**
   * Check if currently streaming
   */
  isStreaming(): boolean {
    return this.session?.isStreaming || false;
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    if (this.session) {
      this.session.isConnected = false;
      this.session.isStreaming = false;
    }

    // Notify status change
    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: 'Stream stopped'
      });
    }
  }
}
