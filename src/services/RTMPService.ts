import { io, Socket } from 'socket.io-client';

interface RTMPServiceConfig {
  url: string;
  streamKey: string;
}

interface RTMPSession {
  sessionId: string;
  isConnected: boolean;
  isStreaming: boolean;
}

export class RTMPService {
  private socket: Socket | null = null;
  private session: RTMPSession | null = null;
  private serviceUrl: string;
  private mediaRecorder: MediaRecorder | null = null;
  private onStatusChange?: (status: { streaming: boolean; message: string }) => void;

  constructor(serviceUrl: string) {
    this.serviceUrl = serviceUrl;
  }

  /**
   * Set callback for status changes
   */
  setStatusCallback(callback: (status: { streaming: boolean; message: string }) => void) {
    this.onStatusChange = callback;
  }

  /**
   * Connect to the RTMP service
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) {
      console.log('🔌 Already connected to RTMP service');
      return;
    }

    console.log(`🔌 Connecting to RTMP service at ${this.serviceUrl}`);

    this.socket = io(this.serviceUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000
    });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('RTMP service connection timeout'));
      }, 10000);

      this.socket!.on('connect', () => {
        clearTimeout(timeout);
        console.log('✅ Connected to RTMP service');
        console.log(`🆔 Socket ID: ${this.socket!.id}`);
        resolve();
      });

      this.socket!.on('connect_error', (error) => {
        clearTimeout(timeout);
        console.error('❌ RTMP service connection error:', error);
        reject(new Error(`RTMP service connection failed: ${error.message}`));
      });

      this.socket!.on('disconnect', (reason) => {
        console.log(`🔌 Disconnected from RTMP service: ${reason}`);
        this.cleanup();
      });

      // Add debugging for all events
      this.socket!.onAny((eventName, ...args) => {
        console.log(`📡 Socket event: ${eventName}`, args);
      });
    });
  }

  /**
   * Start streaming to RTMP endpoint
   */
  async startStreaming(config: RTMPServiceConfig): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('Not connected to RTMP service');
    }

    console.log(`🚀 Starting RTMP stream to ${config.url}`);

    // Request stream session
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Stream session creation timeout'));
      }, 15000);

      this.socket!.emit('start-stream', {
        rtmpUrl: config.url,
        streamKey: config.streamKey,
        roomId: 'host-stream' // Could be dynamic based on room
      });

      this.socket!.on('stream-session-created', ({ sessionId, success, error }) => {
        clearTimeout(timeout);
        
        if (success) {
          this.session = {
            sessionId,
            isConnected: false,
            isStreaming: false
          };
          console.log(`✅ RTMP session created: ${sessionId}`);
          resolve();
        } else {
          console.error(`❌ Failed to create RTMP session: ${error}`);
          reject(new Error(`Failed to create RTMP session: ${error}`));
        }
      });
    });
  }

  /**
   * Setup video streaming via MediaRecorder and WebSocket
   */
  async setupVideoStreaming(canvas: HTMLCanvasElement): Promise<void> {
    if (!this.session) {
      throw new Error('No active RTMP session');
    }

    console.log(`🎥 Setting up MediaRecorder streaming for session ${this.session.sessionId}`);
    console.log(`📐 Canvas dimensions: ${canvas.width}x${canvas.height}`);

    // Verify canvas is ready
    if (canvas.width === 0 || canvas.height === 0) {
      throw new Error('Canvas has invalid dimensions');
    }

    // Get media stream from canvas
    const stream = canvas.captureStream(30); // 30 FPS

    // Start MediaRecorder-based streaming
    await this.startMediaRecorderStreaming(stream);

    // Update session state
    if (this.session) {
      this.session.isConnected = true;
      this.session.isStreaming = true;
    }

    // Notify status change
    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: true,
        message: 'Streaming high-quality video to RTMP service'
      });
    }
  }

  /**
   * Start MediaRecorder-based streaming with adaptive format selection
   */
  private async startMediaRecorderStreaming(stream: MediaStream): Promise<void> {
    if (!this.socket || !this.session) {
      console.error('❌ Cannot start MediaRecorder: missing socket or session');
      return;
    }

    console.log(`🎬 Starting MediaRecorder streaming`);
    console.log(`🔌 Socket connected: ${this.socket.connected}`);
    console.log(`📺 Session ID: ${this.session.sessionId}`);

    // Detect optimal format for this browser
    const { mimeType, container } = this.detectOptimalFormat();
    console.log(`📋 Using format: ${mimeType} (${container})`);

    // Create MediaRecorder with high-quality settings
    const mediaRecorder = new MediaRecorder(stream, {
      mimeType: mimeType,
      videoBitsPerSecond: 8000000, // 8 Mbps for high quality
      audioBitsPerSecond: 128000   // 128 kbps for audio
    });

    let chunkCount = 0;
    let totalDataSent = 0;
    let lastLogTime = Date.now();

    // Handle data available (video chunks)
    mediaRecorder.ondataavailable = (event) => {
      if (event.data && event.data.size > 0) {
        if (!this.socket || !this.session || !this.session.isStreaming) {
          return;
        }

        try {
          // Send binary data directly via WebSocket
          if (this.socket.connected) {
            // Convert Blob to ArrayBuffer for binary transmission
            event.data.arrayBuffer().then((arrayBuffer) => {
              if (this.socket && this.socket.connected && this.session) {
                this.socket.emit('video-chunk', {
                  sessionId: this.session.sessionId,
                  data: arrayBuffer,
                  format: container,
                  timestamp: Date.now()
                });

                chunkCount++;
                totalDataSent += arrayBuffer.byteLength;

                // Log progress every second
                const now = Date.now();
                if (now - lastLogTime > 1000) {
                  const mbSent = (totalDataSent / (1024 * 1024)).toFixed(2);
                  console.log(`📊 Sent ${chunkCount} chunks, ${mbSent}MB total`);
                  lastLogTime = now;
                  chunkCount = 0;
                  totalDataSent = 0;
                }
              }
            }).catch((error) => {
              console.error('❌ Error converting chunk to ArrayBuffer:', error);
            });
          } else {
            console.warn('⚠️ Socket disconnected, skipping chunk');
            this.handleDisconnection();
          }
        } catch (error) {
          console.error('❌ Error sending video chunk:', error);
        }
      }
    };

    // Handle MediaRecorder events
    mediaRecorder.onstart = () => {
      console.log(`✅ MediaRecorder started with ${mimeType}`);
    };

    mediaRecorder.onstop = () => {
      console.log(`🛑 MediaRecorder stopped`);
    };

    mediaRecorder.onerror = (event) => {
      console.error('❌ MediaRecorder error:', event);
      if (this.onStatusChange) {
        this.onStatusChange({
          streaming: false,
          message: `MediaRecorder error: ${event}`
        });
      }
    };

    // Start recording with small time slices for low latency
    mediaRecorder.start(100); // 100ms chunks for low latency

    // Store reference for cleanup
    this.mediaRecorder = mediaRecorder;

    console.log(`🚀 MediaRecorder streaming started!`);
  }

  /**
   * Detect the optimal video format for the current browser
   */
  private detectOptimalFormat(): { mimeType: string; container: string } {
    // Test formats in order of preference
    const formats = [
      { mimeType: 'video/webm;codecs=vp9,opus', container: 'webm' },
      { mimeType: 'video/webm;codecs=vp8,opus', container: 'webm' },
      { mimeType: 'video/webm', container: 'webm' },
      { mimeType: 'video/mp4;codecs=h264,aac', container: 'mp4' },
      { mimeType: 'video/mp4', container: 'mp4' }
    ];

    for (const format of formats) {
      if (MediaRecorder.isTypeSupported(format.mimeType)) {
        console.log(`✅ Browser supports: ${format.mimeType}`);
        return format;
      } else {
        console.log(`❌ Browser does not support: ${format.mimeType}`);
      }
    }

    // Fallback - let browser choose
    console.log(`⚠️ Using browser default format`);
    return { mimeType: '', container: 'unknown' };
  }

  /**
   * Stop streaming
   */
  async stopStreaming(): Promise<void> {
    if (!this.session || !this.socket) {
      return;
    }

    console.log(`🛑 Stopping RTMP stream for session ${this.session.sessionId}`);

    // Stop MediaRecorder first
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
      this.mediaRecorder = null;
    }

    this.socket.emit('stop-stream', {
      sessionId: this.session.sessionId
    });

    // Wait for confirmation
    return new Promise((resolve) => {
      const timeout = setTimeout(resolve, 5000); // 5 second timeout

      this.socket!.on('stream-stopped', ({ sessionId, success, error }) => {
        if (sessionId === this.session!.sessionId) {
          clearTimeout(timeout);
          if (success) {
            console.log(`✅ RTMP stream stopped successfully`);
          } else {
            console.error(`❌ Error stopping RTMP stream: ${error}`);
          }
          this.cleanup();
          resolve();
        }
      });
    });
  }

  /**
   * Disconnect from service
   */
  disconnect(): void {
    console.log('🔌 Disconnecting from RTMP service');
    this.cleanup();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Get current session status
   */
  getStatus(): RTMPSession | null {
    return this.session;
  }

  /**
   * Check if currently streaming
   */
  isStreaming(): boolean {
    return this.session?.isStreaming || false;
  }

  /**
   * Handle disconnection and attempt recovery
   */
  private handleDisconnection(): void {
    console.log('🔄 Attempting to reconnect to RTMP service...');

    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: 'Connection lost, attempting to reconnect...'
      });
    }

    // Try to reconnect after a short delay
    setTimeout(async () => {
      try {
        await this.connect();
        console.log('✅ Reconnected to RTMP service');

        if (this.onStatusChange) {
          this.onStatusChange({
            streaming: true,
            message: 'Reconnected, resuming stream'
          });
        }
      } catch (error) {
        console.error('❌ Failed to reconnect:', error);
        this.cleanup();
      }
    }, 2000);
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    // Stop MediaRecorder
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
      this.mediaRecorder = null;
    }

    if (this.session) {
      this.session.isConnected = false;
      this.session.isStreaming = false;
    }

    // Notify status change
    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: 'Stream stopped'
      });
    }
  }
}
