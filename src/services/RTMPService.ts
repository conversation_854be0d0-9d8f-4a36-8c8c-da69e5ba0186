import { io, Socket } from 'socket.io-client';

interface RTMPServiceConfig {
  url: string;
  streamKey: string;
}

interface RTMPSession {
  sessionId: string;
  isConnected: boolean;
  isStreaming: boolean;
}

export class RTMPService {
  private socket: Socket | null = null;
  private peerConnection: RTCPeerConnection | null = null;
  private session: RTMPSession | null = null;
  private serviceUrl: string;
  private onStatusChange?: (status: { streaming: boolean; message: string }) => void;

  constructor(serviceUrl: string) {
    this.serviceUrl = serviceUrl;
  }

  /**
   * Set callback for status changes
   */
  setStatusCallback(callback: (status: { streaming: boolean; message: string }) => void) {
    this.onStatusChange = callback;
  }

  /**
   * Connect to the RTMP service
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) {
      return;
    }

    console.log(`🔌 Connecting to RTMP service at ${this.serviceUrl}`);

    this.socket = io(this.serviceUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000
    });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('RTMP service connection timeout'));
      }, 10000);

      this.socket!.on('connect', () => {
        clearTimeout(timeout);
        console.log('✅ Connected to RTMP service');
        resolve();
      });

      this.socket!.on('connect_error', (error) => {
        clearTimeout(timeout);
        console.error('❌ RTMP service connection error:', error);
        reject(new Error(`RTMP service connection failed: ${error.message}`));
      });

      this.socket!.on('disconnect', () => {
        console.log('🔌 Disconnected from RTMP service');
        this.cleanup();
      });
    });
  }

  /**
   * Start streaming to RTMP endpoint
   */
  async startStreaming(config: RTMPServiceConfig): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('Not connected to RTMP service');
    }

    console.log(`🚀 Starting RTMP stream to ${config.url}`);

    // Request stream session
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Stream session creation timeout'));
      }, 15000);

      this.socket!.emit('start-stream', {
        rtmpUrl: config.url,
        streamKey: config.streamKey,
        roomId: 'host-stream' // Could be dynamic based on room
      });

      this.socket!.on('stream-session-created', ({ sessionId, success, error }) => {
        clearTimeout(timeout);
        
        if (success) {
          this.session = {
            sessionId,
            isConnected: false,
            isStreaming: false
          };
          console.log(`✅ RTMP session created: ${sessionId}`);
          resolve();
        } else {
          console.error(`❌ Failed to create RTMP session: ${error}`);
          reject(new Error(`Failed to create RTMP session: ${error}`));
        }
      });
    });
  }

  /**
   * Establish WebRTC connection to send video stream
   */
  async setupWebRTCConnection(videoStream: MediaStream): Promise<void> {
    if (!this.session) {
      throw new Error('No active RTMP session');
    }

    console.log(`🔗 Setting up WebRTC connection for session ${this.session.sessionId}`);

    // Create peer connection
    this.peerConnection = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        // Add TURN server if configured
        ...(import.meta.env.VITE_TURN_SERVER ? [{
          urls: `turn:${import.meta.env.VITE_TURN_SERVER}:3478`,
          username: import.meta.env.VITE_TURN_USERNAME || 'webrtc',
          credential: import.meta.env.VITE_TURN_PASSWORD || 'webrtc123'
        }] : [])
      ]
    });

    // Add video stream to peer connection
    videoStream.getTracks().forEach(track => {
      this.peerConnection!.addTrack(track, videoStream);
    });

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.socket) {
        this.socket.emit('webrtc-ice-candidate', {
          sessionId: this.session!.sessionId,
          candidate: event.candidate
        });
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      const state = this.peerConnection!.connectionState;
      console.log(`🔄 WebRTC connection state: ${state}`);
      
      if (this.session) {
        this.session.isConnected = state === 'connected';
        this.session.isStreaming = state === 'connected';
      }

      // Notify status change
      if (this.onStatusChange) {
        this.onStatusChange({
          streaming: state === 'connected',
          message: state === 'connected' ? 'Streaming to RTMP' : `Connection state: ${state}`
        });
      }

      if (state === 'failed' || state === 'disconnected') {
        this.cleanup();
      }
    };

    // Create and send offer
    const offer = await this.peerConnection.createOffer();
    await this.peerConnection.setLocalDescription(offer);

    // Send offer to RTMP service
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('WebRTC offer timeout'));
      }, 10000);

      this.socket!.emit('webrtc-offer', {
        sessionId: this.session!.sessionId,
        offer: offer
      });

      this.socket!.on('webrtc-answer', async ({ sessionId, answer }) => {
        if (sessionId === this.session!.sessionId) {
          clearTimeout(timeout);
          try {
            await this.peerConnection!.setRemoteDescription(answer);
            console.log(`✅ WebRTC connection established for session ${sessionId}`);
            resolve();
          } catch (error) {
            console.error(`❌ Error setting remote description:`, error);
            reject(error);
          }
        }
      });

      this.socket!.on('webrtc-error', ({ sessionId, error }) => {
        if (sessionId === this.session!.sessionId) {
          clearTimeout(timeout);
          console.error(`❌ WebRTC error for session ${sessionId}:`, error);
          reject(new Error(`WebRTC error: ${error}`));
        }
      });
    });
  }

  /**
   * Stop streaming
   */
  async stopStreaming(): Promise<void> {
    if (!this.session || !this.socket) {
      return;
    }

    console.log(`🛑 Stopping RTMP stream for session ${this.session.sessionId}`);

    this.socket.emit('stop-stream', {
      sessionId: this.session.sessionId
    });

    // Wait for confirmation
    return new Promise((resolve) => {
      const timeout = setTimeout(resolve, 5000); // 5 second timeout

      this.socket!.on('stream-stopped', ({ sessionId, success, error }) => {
        if (sessionId === this.session!.sessionId) {
          clearTimeout(timeout);
          if (success) {
            console.log(`✅ RTMP stream stopped successfully`);
          } else {
            console.error(`❌ Error stopping RTMP stream: ${error}`);
          }
          this.cleanup();
          resolve();
        }
      });
    });
  }

  /**
   * Disconnect from service
   */
  disconnect(): void {
    console.log('🔌 Disconnecting from RTMP service');
    this.cleanup();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Get current session status
   */
  getStatus(): RTMPSession | null {
    return this.session;
  }

  /**
   * Check if currently streaming
   */
  isStreaming(): boolean {
    return this.session?.isStreaming || false;
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.session) {
      this.session.isConnected = false;
      this.session.isStreaming = false;
    }

    // Notify status change
    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: 'Stream stopped'
      });
    }
  }
}
